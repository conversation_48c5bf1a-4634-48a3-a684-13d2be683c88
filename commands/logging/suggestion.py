import discord
from discord.ext import commands
from discord import app_commands
from botmain import *
from typing import Literal

# Static variables
sticky_message = None
SUGGESTION_CHANNEL_ID = 1319058341187555378  
# SUGGESTION_CHANNEL_ID = 1343671852274286652  # Global suggestion channel ID

class SuggestionModal(discord.ui.Modal, title="Submit a Suggestion"):
    def __init__(self):
        super().__init__()
        self.suggestion = discord.ui.TextInput(
            label="Your Suggestion",
            placeholder="Enter your suggestion here...",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=1000
        )
        self.add_item(self.suggestion)

    async def on_submit(self, interaction: discord.Interaction):
        global sticky_message
        suggestion_channel = interaction.client.get_channel(SUGGESTION_CHANNEL_ID)
        
        if not suggestion_channel:
            await interaction.response.send_message("Error: Suggestion channel not found.", ephemeral=True)
            return
            
        embed = discord.Embed(
            title="Suggestion",
            description=self.suggestion.value,
            color=0x2b2d31,
        )
        embed.set_author(name=interaction.user.display_name, icon_url=interaction.user.display_avatar.url)
                
        msg = await suggestion_channel.send(embed=embed)
        await interaction.response.send_message("Your suggestion has been submitted. Thank you!", ephemeral=True)

        await msg.add_reaction("⬆️")
        await msg.add_reaction("⬇️")
        
        if sticky_message:
            try:
                await sticky_message.delete()
            except discord.NotFound:
                pass
            except Exception as e:
                print(f"Error deleting sticky message: {e}")
        
        embed = discord.Embed(
            title="Suggestions",
            description="Click the button below to submit a suggestion!",
            color=0x2b2d31
        )
        
        view = SuggestionButton()
        sticky_message = await suggestion_channel.send(embed=embed, view=view)

class SuggestionButton(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)
    
    @discord.ui.button(label="Suggest", style=discord.ButtonStyle.secondary, custom_id="suggest_button")
    async def suggest_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_modal(SuggestionModal())

class Suggestion(commands.Cog):
    def __init__(self, client):
        self.client = client
    
    async def cog_load(self):
        self.client.add_view(SuggestionButton())
    
    @commands.hybrid_command()
    @commands.has_permissions(manage_guild=True)
    async def setupsuggestion(self, ctx):
        """Creates a sticky message with a suggestion button in the suggestion channel"""
        global sticky_message
        channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)
        
        if not channel:
            await ctx.send("Error: Suggestion channel not found.")
            return
            

        if sticky_message:
            try:
                await sticky_message.delete()
            except discord.NotFound:
                pass
            except Exception as e:
                print(f"Error deleting sticky message: {e}")
            
        embed = discord.Embed(
            title="Suggestions",
            description="Click the button below to submit a suggestion!",
            color=0x2b2d31
        )
        
        view = SuggestionButton()
        sticky_message = await channel.send(embed=embed, view=view)
        
        await ctx.send(f"Suggestion message set up in {channel.mention}!")
    
    @commands.hybrid_command()
    @commands.check(check_app_command_permission)
    async def reply(self, ctx, message_id: str, status: Literal["Approve", "Denied", "Considered", "Empty"] = "Empty", *, reason: str = None):
        """Reply to a suggestion with a status and reason
        
        Parameters
        -----------
        message_id: The ID of the suggestion message to reply to
        status: The status to set (Approve, Denied, Considered, or Empty to reset)
        reason: The reason for the decision (optional)
        """
        channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)
        
        if not channel:
            await ctx.send("Error: Suggestion channel not found.")
            return
        
        try:
     
            message = await channel.fetch_message(int(message_id))
            
           
            if not message.embeds or len(message.embeds) == 0:
                await ctx.send("Error: This message is not a suggestion.")
                return
            
            original_embed = message.embeds[0]
            
            embed = discord.Embed(
                description=original_embed.description,
            )
            
            if original_embed.author:
                embed.set_author(
                    name=original_embed.author.name,
                    icon_url=original_embed.author.icon_url
                )
          
            if original_embed.footer:
                embed.set_footer(
                    text=original_embed.footer.text,
                    icon_url=original_embed.footer.icon_url
                )
           
            if status == "Approve":
                embed.title = "Suggestion | Approved"
                embed.color = discord.Color.green()
            elif status == "Denied":
                embed.title = "Suggestion | Denied"
                embed.color = discord.Color.red()
            elif status == "Considered":
                embed.title = "Suggestion | Considered"
                embed.color = discord.Color.gold()
            else:  # Empty - reset to default
                embed.title = "Suggestion"
                embed.color = 0x2b2d31
                
                await message.edit(embed=embed)
                await ctx.send("Suggestion reset to its original state." , ephemeral=True)
                return
            
            
            for field in original_embed.fields:
                if field.name.startswith("Response from"):
                    continue  # Skip existing response fields
                embed.add_field(name=field.name, value=field.value, inline=field.inline)
            
         
            if reason:
                embed.add_field(
                    name=f"Response from {ctx.author.display_name}",
                    value=reason,
                    inline=False
                )
            
            await message.edit(embed=embed)
            await ctx.send(f"Suggestion updated with status: {status}\n\nReason: {reason}" , ephemeral=True)
            
        except discord.NotFound:
            await ctx.send("Error: Message not found. Make sure you provided the correct message ID." , delete_after=10 , ephemeral=True)
        except Exception as e:
            await ctx.send(f"An error occurred: {str(e)}" , delete_after=10 , ephemeral=True)
    
async def setup(client):
    await client.add_cog(Suggestion(client)) 